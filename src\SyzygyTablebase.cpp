#include "SyzygyTablebase.h"
#include "ZobristHash.h"
#include <algorithm>
#include <iostream>

// Include Fathom library
// TODO: Install fathom library for Syzygy tablebase support
// extern "C" {
//     #include "fathom/src/tbprobe.h"
// }

SyzygyTablebase::SyzygyTablebase()
    : maxPieces_(0), maxCacheSize_(32 * 1024 * 1024), // 32 MB default cache
      tbCache(nullptr, [](TbCache *) {}),
      probeCount_(0), cacheHits_(0), probeFailed_(0), initialized_(false)
{
    // Reserve cache space
    tbCache_.reserve(1000000);
}

SyzygyTablebase::~SyzygyTablebase()
{
    // Cleanup is handled by unique_ptr
}

bool SyzygyTablebase::initialize(const std::vector<std::string> &paths, int maxPieces)
{
    if (paths.empty())
    {
        return false;
    }

    tbPaths_ = paths;
    maxPieces_ = std::min(max<PERSON><PERSON><PERSON>, 7); // Maximum 7 pieces supported by Syzygy

    // Convert paths to C-style strings for Fathom
    std::vector<const char *> pathsCStr;
    for (const auto &path : tbPaths_)
    {
        pathsCStr.push_back(path.c_str());
    }

    // Initialize Fathom library
    // TODO: Uncomment when fathom library is available
    // tb_init(pathsCStr.data(), pathsCStr.size());

    // Check if initialization was successful
    // TODO: Uncomment when fathom library is available
    // if (TB_LARGEST == 0)
    // {
    //     std::cerr << "Failed to initialize Syzygy tablebases" << std::endl;
    //     return false;
    // }

    // Limit to what's actually available
    // TODO: Uncomment when fathom library is available
    // maxPieces_ = std::min(maxPieces_, TB_LARGEST);

    // For now, just disable tablebase functionality
    maxPieces_ = 0;

    std::cout << "Syzygy tablebases disabled (fathom library not available)" << std::endl;

    initialized_ = false; // Set to false since we can't actually initialize
    return false;
}

void SyzygyTablebase::setCacheSize(size_t sizeMB)
{
    maxCacheSize_ = sizeMB * 1024 * 1024;
    clearCache();
}

void SyzygyTablebase::clearCache()
{
    std::lock_guard<std::mutex> lock(cacheMutex_);
    tbCache_.clear();
}

bool SyzygyTablebase::isTablebasePosition(const ChessBoard &board) const
{
    // TODO: Uncomment when fathom library is available
    // if (!initialized_ || TB_LARGEST == 0)
    // {
    //     return false;
    // }

    // For now, always return false since tablebase is disabled
    return false;

    // TODO: Uncomment when fathom library is available
    // Count pieces
    // int pieceCount = 0;
    // for (int rank = 0; rank < 8; ++rank)
    // {
    //     for (int file = 0; file < 8; ++file)
    //     {
    //         const Piece *piece = board.getPiece(Position(rank, file));
    //         if (piece)
    //         {
    //             pieceCount++;
    //         }
    //     }
    // }

    // Check if position has few enough pieces for the tablebase
    // return pieceCount <= maxPieces_ && pieceCount <= TB_LARGEST;
}

bool SyzygyTablebase::boardToTbPosition(const ChessBoard &board, Color sideToMove, TbPosition &pos)
{
    // TODO: Implement when fathom library is available
    // For now, always return false
    return false;

    // TODO: Uncomment when fathom library is available
    // Set side to move
    // pos.turn = (sideToMove == Color::WHITE) ? 0 : 1;

    // TODO: Uncomment when fathom library is available
    /*
    // Set pieces
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = board.getPiece(Position(rank, file));
            if (!piece)
                continue;

            int square = rank * 8 + file;
            int color = (piece->getColor() == Color::WHITE) ? 0 : 1;

            switch (piece->getType())
            {
            case PieceType::PAWN:
                pos.pawns[color] |= (1ULL << square);
                break;
            case PieceType::KNIGHT:
                pos.knights[color] |= (1ULL << square);
                break;
            case PieceType::BISHOP:
                pos.bishops[color] |= (1ULL << square);
                break;
            case PieceType::ROOK:
                pos.rooks[color] |= (1ULL << square);
                break;
            case PieceType::QUEEN:
                pos.queens[color] |= (1ULL << square);
                break;
            case PieceType::KING:
                pos.kings[color] |= (1ULL << square);
                break;
            default:
                break;
            }
        }
    }

    // Set castling rights
    pos.castling = 0;
    if (board.canCastleKingside(Color::WHITE))
        pos.castling |= 1;
    if (board.canCastleQueenside(Color::WHITE))
        pos.castling |= 2;
    if (board.canCastleKingside(Color::BLACK))
        pos.castling |= 4;
    if (board.canCastleQueenside(Color::BLACK))
        pos.castling |= 8;

    // Set en passant square
    Position epSquare = board.getEnPassantSquare();
    if (epSquare.isValid())
    {
        pos.ep = epSquare.rank * 8 + epSquare.file;
    }
    else
    {
        pos.ep = 0;
    }

    // Set 50-move rule counter
    pos.rule50 = board.getHalfMoveClock();

    return true;
    */
}

Move SyzygyTablebase::tbMoveToMove(unsigned int tbMove, const ChessBoard &board)
{
    // TODO: Implement when fathom library is available
    // For now, return invalid move
    return Move();

    Position from(fromSquare / 8, fromSquare % 8);
    Position to(toSquare / 8, toSquare % 8);

    // Get promotion piece if any
    PieceType promotion = PieceType::NONE;
    int tbPromo = TB_GET_PROMOTES(tbMove);

    switch (tbPromo)
    {
    case TB_PROMOTES_QUEEN:
        promotion = PieceType::QUEEN;
        break;
    case TB_PROMOTES_ROOK:
        promotion = PieceType::ROOK;
        break;
    case TB_PROMOTES_BISHOP:
        promotion = PieceType::BISHOP;
        break;
    case TB_PROMOTES_KNIGHT:
        promotion = PieceType::KNIGHT;
        break;
    }

    // Check for special moves
    const Piece *piece = board.getPiece(from);
    if (!piece)
        return Move(); // Invalid move

    MoveType moveType = MoveType::NORMAL;

    // Check for castling
    if (piece->getType() == PieceType::KING)
    {
        if (from.file == 4 && to.file == 6)
        {
            moveType = MoveType::CASTLE_KINGSIDE;
        }
        else if (from.file == 4 && to.file == 2)
        {
            moveType = MoveType::CASTLE_QUEENSIDE;
        }
    }

    // Check for en passant
    if (piece->getType() == PieceType::PAWN)
    {
        Position epSquare = board.getEnPassantSquare();
        if (epSquare.isValid() && to == epSquare)
        {
            moveType = MoveType::EN_PASSANT;
        }
    }

    // Check for promotion
    if (promotion != PieceType::NONE)
    {
        moveType = MoveType::PAWN_PROMOTION;
    }

    return Move(from, to, moveType, promotion);
}

unsigned int SyzygyTablebase::moveToTbMove(const Move &move)
{
    int fromSquare = move.from.rank * 8 + move.from.file;
    int toSquare = move.to.rank * 8 + move.to.file;

    unsigned int tbMove = TB_MOVE(fromSquare, toSquare, 0);

    // Set promotion piece if any
    if (move.type == MoveType::PAWN_PROMOTION)
    {
        switch (move.promotion)
        {
        case PieceType::QUEEN:
            tbMove |= TB_PROMOTES_QUEEN;
            break;
        case PieceType::ROOK:
            tbMove |= TB_PROMOTES_ROOK;
            break;
        case PieceType::BISHOP:
            tbMove |= TB_PROMOTES_BISHOP;
            break;
        case PieceType::KNIGHT:
            tbMove |= TB_PROMOTES_KNIGHT;
            break;
        default:
            break;
        }
    }

    return tbMove;
}

void SyzygyTablebase::cacheResult(uint64_t hash, const ProbeData &data)
{
    std::lock_guard<std::mutex> lock(cacheMutex_);

    // Check if cache is full
    if (tbCache_.size() >= maxCacheSize_ / sizeof(CacheEntry))
    {
        // Simple strategy: clear half the cache
        size_t toRemove = tbCache_.size() / 2;
        auto it = tbCache_.begin();
        for (size_t i = 0; i < toRemove && it != tbCache_.end(); ++i)
        {
            it = tbCache_.erase(it);
        }
    }

    // Add to cache
    tbCache_[hash] = CacheEntry(hash, data);
}

bool SyzygyTablebase::checkCache(uint64_t hash, ProbeData &data)
{
    std::lock_guard<std::mutex> lock(cacheMutex_);

    auto it = tbCache_.find(hash);
    if (it != tbCache_.end())
    {
        data = it->second.data;
        cacheHits_++;
        return true;
    }

    return false;
}

bool SyzygyTablebase::probeWDL(const ChessBoard &board, Color sideToMove, ProbeData &result)
{
    // TODO: Implement when fathom library is available
    // For now, always return false (no tablebase data available)
    return false;

    probeCount_++;

    // Generate position hash
    uint64_t hash = ZobristHash::generateHash(board);

    // Check cache first
    if (checkCache(hash, result))
    {
        return true;
    }

    // Convert board to Fathom position
    TbPosition pos;
    if (!boardToTbPosition(board, sideToMove, pos))
    {
        probeFailed_++;
        return false;
    }

    // Probe WDL
    unsigned int wdl = tb_probe_wdl(&pos);

    // Check for probe failure
    if (wdl == TB_RESULT_FAILED)
    {
        probeFailed_++;
        return false;
    }

    // Convert result
    switch (wdl)
    {
    case TB_WIN:
        result.result = ProbeResult::WIN;
        result.score = 9000; // Near mate score
        break;
    case TB_LOSS:
        result.result = ProbeResult::LOSS;
        result.score = -9000;
        break;
    case TB_DRAW:
        result.result = ProbeResult::DRAW;
        result.score = 0;
        break;
    case TB_CURSED_WIN:
        result.result = ProbeResult::CURSED_WIN;
        result.score = 1; // Slight advantage but not winning
        break;
    case TB_BLESSED_LOSS:
        result.result = ProbeResult::BLESSED_LOSS;
        result.score = -1;
        break;
    }

    // Cache result
    cacheResult(hash, result);

    return true;
}

bool SyzygyTablebase::probeDTZ(const ChessBoard &board, Color sideToMove, ProbeData &result)
{
    // TODO: Implement when fathom library is available
    // For now, always return false (no tablebase data available)
    return false;

    probeCount_++;

    // Generate position hash
    uint64_t hash = ZobristHash::generateHash(board);

    // Check cache first
    if (checkCache(hash, result))
    {
        return true;
    }

    // Convert board to Fathom position
    TbPosition pos;
    if (!boardToTbPosition(board, sideToMove, pos))
    {
        probeFailed_++;
        return false;
    }

    // Probe DTZ
    unsigned int dtz = tb_probe_dtz(&pos);

    // Check for probe failure
    if (dtz == TB_RESULT_FAILED)
    {
        probeFailed_++;
        return false;
    }

    // Extract WDL and DTZ values
    unsigned int wdl = TB_GET_WDL(dtz);
    unsigned int dtzmove = TB_GET_DTZ(dtz);

    // Convert result
    switch (wdl)
    {
    case TB_WIN:
        result.result = ProbeResult::WIN;
        result.score = 9000 - dtzmove; // Prefer shorter wins
        break;
    case TB_LOSS:
        result.result = ProbeResult::LOSS;
        result.score = -9000 + dtzmove; // Prefer longer losses
        break;
    case TB_DRAW:
        result.result = ProbeResult::DRAW;
        result.score = 0;
        break;
    case TB_CURSED_WIN:
        result.result = ProbeResult::CURSED_WIN;
        result.score = 1;
        break;
    case TB_BLESSED_LOSS:
        result.result = ProbeResult::BLESSED_LOSS;
        result.score = -1;
        break;
    }

    result.dtz = dtzmove;

    // Cache result
    cacheResult(hash, result);

    return true;
}

bool SyzygyTablebase::probeRoot(const ChessBoard &board, Color sideToMove, std::vector<Move> &moves, ProbeData &result)
{
    // TODO: Implement when fathom library is available
    // For now, always return false (no tablebase data available)
    return false;

    probeCount_++;

    // Convert board to Fathom position
    TbPosition pos;
    if (!boardToTbPosition(board, sideToMove, pos))
    {
        probeFailed_++;
        return false;
    }

    // Convert moves to Fathom moves
    std::vector<unsigned int> tbMoves;
    for (const auto &move : moves)
    {
        tbMoves.push_back(moveToTbMove(move));
    }

    // Prepare root moves structure
    TbRootMoves tbRootMoves;
    tbRootMoves.count = std::min(static_cast<unsigned int>(tbMoves.size()), TB_MAX_MOVES);
    for (unsigned int i = 0; i < tbRootMoves.count; i++)
    {
        tbRootMoves.moves[i].move = tbMoves[i];
    }

    // Probe root
    unsigned int res = tb_probe_root(&pos, &tbRootMoves);

    // Check for probe failure
    if (res == TB_RESULT_FAILED)
    {
        probeFailed_++;
        return false;
    }

    // Find best move
    int bestScore = -10000;
    unsigned int bestTbMove = 0;

    for (unsigned int i = 0; i < tbRootMoves.count; i++)
    {
        int score = tbRootMoves.moves[i].score;
        if (score > bestScore)
        {
            bestScore = score;
            bestTbMove = tbRootMoves.moves[i].move;
        }
    }

    // Convert best move
    result.bestMove = tbMoveToMove(bestTbMove, board);

    // Set score based on WDL
    unsigned int wdl = TB_GET_WDL(res);
    switch (wdl)
    {
    case TB_WIN:
        result.result = ProbeResult::WIN;
        result.score = bestScore;
        break;
    case TB_LOSS:
        result.result = ProbeResult::LOSS;
        result.score = bestScore;
        break;
    case TB_DRAW:
        result.result = ProbeResult::DRAW;
        result.score = 0;
        break;
    case TB_CURSED_WIN:
        result.result = ProbeResult::CURSED_WIN;
        result.score = 1;
        break;
    case TB_BLESSED_LOSS:
        result.result = ProbeResult::BLESSED_LOSS;
        result.score = -1;
        break;
    }

    return true;
}